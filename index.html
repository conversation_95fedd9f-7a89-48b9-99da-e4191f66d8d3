<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>万和隆集团 | 全案服务，构筑空间之美</title>
    <!-- Chosen Palette: Warm Harmony -->
    <!-- Application Structure Plan: A top-level navigation SPA with smooth scrolling. The structure follows the source's logical division into About, Commercial, Residential, and OEM/ODM sections. This is intuitive for different user types (e.g., a hotel developer can go straight to "Commercial"). Interactive cards in the "About" section act as a visual directory. The design uses visual process flows and charts to make dense information more digestible than plain text, enhancing usability. Gemini API features are added to the Commercial and Residential sections to provide interactive, value-added tools for users, showcasing design expertise. Multilingual support (EN/ZH) is added with a UI switcher and a centralized translation object in JS, making the site globally accessible. -->
    <!-- Visualization & Content Choices: 1. Commercial Services: Goal: Compare/Inform/Engage. Method: Horizontal Bar Chart (Chart.js) + a Gemini API-powered "AI Space Concept Generator". Interaction: Hover tooltips on chart; text input and button click for AI generation. Justification: The chart provides a quick overview, while the AI generator offers a deep, personalized engagement, demonstrating design leadership. 2. Residential Services: Goal: Organize/Inform/Engage. Method: HTML/CSS visual process flow + a placeholder for a Gemini API "AI Lifestyle Analyst". Interaction: Hover highlights on process; disabled input for future AI feature. Justification: The flow explains the 'how', while the AI feature placeholder hints at a deeper level of personalization ('why'). 3. OEM/ODM Services: Goal: Inform. Method: Doughnut Chart (Chart.js). Interaction: Hover tooltips. Justification: Quickly communicates the balance of their B2B services. All text elements, including chart labels and API prompts, are now dynamically updated based on the selected language. CONFIRMATION: NO SVG/Mermaid used. -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #FDFBF8;
            color: #333333;
        }
        .nav-link {
            position: relative;
            transition: color 0.3s ease;
        }
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -4px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #C0A080;
            transition: width 0.3s ease;
        }
        .nav-link.active, .nav-link:hover {
            color: #C0A080;
        }
        .nav-link.active::after, .nav-link:hover::after {
            width: 100%;
        }
        .hero-bg {
            background-image: url('https://placehold.co/1920x1080/FDFBF8/333333?text=WANHELONG+GROUP');
            background-size: cover;
            background-position: center;
        }
        .section-title {
            font-size: 2.25rem;
            font-weight: 700;
            color: #333333;
            margin-bottom: 1rem;
            text-align: center;
        }
        .section-subtitle {
            font-size: 1rem;
            font-weight: 300;
            color: #888888;
            margin-bottom: 4rem;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        .gemini-output h3 {
            font-size: 1.25rem;
            font-weight: bold;
            color: #C0A080;
            margin-top: 1rem;
            margin-bottom: 0.5rem;
        }
        .gemini-output p {
            margin-bottom: 0.5rem;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #C0A080;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .lang-btn {
            padding: 0.25rem 0.75rem;
            border: 1px solid #ddd;
            background-color: transparent;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .lang-btn.active {
            background-color: #C0A080;
            color: white;
            border-color: #C0A080;
        }
    </style>
</head>
<body class="antialiased">

    <header id="header" class="bg-white/80 backdrop-blur-md sticky top-0 z-50 shadow-sm">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <a href="#" class="text-2xl font-bold text-gray-800" data-translate-key="brandName">万和隆集团</a>
            <div class="hidden md:flex items-center space-x-8">
                <a href="#about" class="nav-link text-gray-600" data-translate-key="navAbout">关于我们</a>
                <a href="#commercial" class="nav-link text-gray-600" data-translate-key="navCommercial">工装服务</a>
                <a href="#residential" class="nav-link text-gray-600" data-translate-key="navResidential">家装服务</a>
                <a href="#oem" class="nav-link text-gray-600" data-translate-key="navOem">代工合作</a>
            </div>
            <div class="flex items-center space-x-4">
                 <div class="hidden md:flex rounded-md border border-gray-300">
                    <button id="lang-zh" class="lang-btn rounded-l-md">中文</button>
                    <button id="lang-en" class="lang-btn rounded-r-md">EN</button>
                </div>
                <a href="#contact" class="hidden md:inline-block bg-[#C0A080] text-white px-5 py-2 rounded-full hover:bg-[#b59270] transition-colors" data-translate-key="navContact">联系我们</a>
            </div>
        </nav>
    </header>

    <main>
        <section id="hero" class="hero-bg h-screen flex items-center justify-center text-white">
            <div class="text-center bg-black/50 p-12 rounded-lg">
                <h1 class="text-5xl md:text-7xl font-bold mb-4" data-translate-key="heroTitle"></h1>
                <p class="text-xl md:text-2xl font-light" data-translate-key="heroSubtitle"></p>
            </div>
        </section>

        <section id="about" class="py-20">
            <div class="container mx-auto px-6">
                <h2 class="section-title" data-translate-key="aboutTitle"></h2>
                <p class="section-subtitle" data-translate-key="aboutSubtitle"></p>
                <div class="max-w-4xl mx-auto text-center text-gray-600 leading-relaxed mb-16">
                    <p data-translate-key="aboutDesc"></p>
                </div>
                <div class="grid md:grid-cols-3 gap-8 text-center">
                    <a href="#commercial" class="group block bg-white p-8 rounded-xl shadow-lg hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
                        <div class="text-4xl text-[#C0A080] mb-4">🏢</div>
                        <h3 class="text-xl font-bold mb-2 text-gray-800" data-translate-key="cardCommercialTitle"></h3>
                        <p class="text-gray-500" data-translate-key="cardCommercialDesc"></p>
                    </a>
                    <a href="#residential" class="group block bg-white p-8 rounded-xl shadow-lg hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
                        <div class="text-4xl text-[#C0A080] mb-4">🏡</div>
                        <h3 class="text-xl font-bold mb-2 text-gray-800" data-translate-key="cardResidentialTitle"></h3>
                        <p class="text-gray-500" data-translate-key="cardResidentialDesc"></p>
                    </a>
                    <a href="#oem" class="group block bg-white p-8 rounded-xl shadow-lg hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
                        <div class="text-4xl text-[#C0A080] mb-4">🏭</div>
                        <h3 class="text-xl font-bold mb-2 text-gray-800" data-translate-key="cardOemTitle"></h3>
                        <p class="text-gray-500" data-translate-key="cardOemDesc"></p>
                    </a>
                </div>
            </div>
        </section>

        <section id="commercial" class="py-20 bg-white">
            <div class="container mx-auto px-6">
                <h2 class="section-title" data-translate-key="commercialTitle"></h2>
                <p class="section-subtitle" data-translate-key="commercialSubtitle"></p>
                <div class="flex flex-col lg:flex-row gap-12 items-center mb-16">
                    <div class="lg:w-1/2">
                        <h3 class="text-2xl font-bold mb-4 text-gray-800" data-translate-key="commercialCoreTitle"></h3>
                        <p class="text-gray-600 mb-6 leading-relaxed" data-translate-key="commercialCoreDesc"></p>
                        <ul class="space-y-4">
                            <li class="p-4 bg-gray-50 rounded-lg">
                                <h4 class="font-bold text-[#C0A080]" data-translate-key="commercialItem1Title"></h4>
                                <p class="text-sm text-gray-500" data-translate-key="commercialItem1Desc"></p>
                            </li>
                            <li class="p-4 bg-gray-50 rounded-lg">
                                <h4 class="font-bold text-gray-700" data-translate-key="commercialItem2Title"></h4>
                                <p class="text-sm text-gray-500" data-translate-key="commercialItem2Desc"></p>
                            </li>
                             <li class="p-4 bg-gray-50 rounded-lg">
                                <h4 class="font-bold text-gray-700" data-translate-key="commercialItem3Title"></h4>
                                <p class="text-sm text-gray-500" data-translate-key="commercialItem3Desc"></p>
                            </li>
                        </ul>
                    </div>
                    <div class="lg:w-1/2 w-full">
                        <div class="chart-container">
                            <canvas id="commercialChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-8 rounded-xl shadow-inner">
                    <h3 class="text-2xl font-bold mb-4 text-center text-gray-800" data-translate-key="aiGeneratorTitle"></h3>
                    <p class="text-center text-gray-600 mb-6" data-translate-key="aiGeneratorDesc"></p>
                    <div class="max-w-2xl mx-auto">
                        <div class="grid md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="spaceType" class="block text-sm font-medium text-gray-700 mb-1" data-translate-key="aiLabelSpaceType"></label>
                                <input type="text" id="spaceType" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#C0A080] focus:border-[#C0A080]" data-translate-key="aiPlaceholderSpaceType" placeholder="">
                            </div>
                            <div>
                                <label for="designStyle" class="block text-sm font-medium text-gray-700 mb-1" data-translate-key="aiLabelDesignStyle"></label>
                                <input type="text" id="designStyle" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#C0A080] focus:border-[#C0A080]" data-translate-key="aiPlaceholderDesignStyle" placeholder="">
                            </div>
                        </div>
                        <button id="generateConceptBtn" class="w-full bg-[#C0A080] text-white px-5 py-3 rounded-md hover:bg-[#b59270] transition-colors font-bold flex items-center justify-center" data-translate-key="aiGenerateBtn"></button>
                        <div id="geminiResult" class="mt-6 p-6 bg-white rounded-md border border-gray-200 min-h-[100px]">
                            <p class="text-gray-400 text-center" data-translate-key="aiResultPlaceholder"></p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="residential" class="py-20">
            <div class="container mx-auto px-6">
                <h2 class="section-title" data-translate-key="residentialTitle"></h2>
                <p class="section-subtitle" data-translate-key="residentialSubtitle"></p>
                <p class="max-w-3xl mx-auto text-center text-gray-600 leading-relaxed mb-16" data-translate-key="residentialDesc"></p>
                <div class="relative mb-16">
                    <div class="hidden md:block absolute top-0 left-1/2 w-0.5 h-full bg-gray-200"></div>
                    <div class="space-y-16">
                         <div class="md:flex items-center md:space-x-8">
                            <div class="md:w-1/2 flex md:justify-end">
                                <div class="w-full md:w-5/6 bg-white p-6 rounded-xl shadow-lg">
                                    <div class="flex items-center mb-2">
                                        <div class="bg-[#C0A080] text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4">1</div>
                                        <h3 class="text-lg font-bold" data-translate-key="resiStep1Title"></h3>
                                    </div>
                                    <p class="text-gray-600 text-sm" data-translate-key="resiStep1Desc"></p>
                                </div>
                            </div>
                            <div class="hidden md:block w-1/2"></div>
                        </div>
                        <div class="md:flex items-center md:space-x-8">
                            <div class="hidden md:block w-1/2"></div>
                            <div class="md:w-1/2 flex md:justify-start">
                                <div class="w-full md:w-5/6 bg-white p-6 rounded-xl shadow-lg">
                                    <div class="flex items-center mb-2">
                                        <div class="bg-[#C0A080] text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4">2</div>
                                        <h3 class="text-lg font-bold" data-translate-key="resiStep2Title"></h3>
                                    </div>
                                    <p class="text-gray-600 text-sm" data-translate-key="resiStep2Desc"></p>
                                </div>
                            </div>
                        </div>
                        <div class="md:flex items-center md:space-x-8">
                            <div class="md:w-1/2 flex md:justify-end">
                                <div class="w-full md:w-5/6 bg-white p-6 rounded-xl shadow-lg">
                                    <div class="flex items-center mb-2">
                                        <div class="bg-[#C0A080] text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4">3</div>
                                        <h3 class="text-lg font-bold" data-translate-key="resiStep3Title"></h3>
                                    </div>
                                    <p class="text-gray-600 text-sm" data-translate-key="resiStep3Desc"></p>
                                </div>
                            </div>
                            <div class="hidden md:block w-1/2"></div>
                        </div>
                        <div class="md:flex items-center md:space-x-8">
                            <div class="hidden md:block w-1/2"></div>
                            <div class="md:w-1/2 flex md:justify-start">
                                <div class="w-full md:w-5/6 bg-white p-6 rounded-xl shadow-lg">
                                    <div class="flex items-center mb-2">
                                        <div class="bg-[#C0A080] text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4">4</div>
                                        <h3 class="text-lg font-bold" data-translate-key="resiStep4Title"></h3>
                                    </div>
                                    <p class="text-gray-600 text-sm" data-translate-key="resiStep4Desc"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 p-8 rounded-xl shadow-inner opacity-60">
                    <h3 class="text-2xl font-bold mb-4 text-center text-gray-800" data-translate-key="aiAnalyzerTitle"></h3>
                    <p class="text-center text-gray-600 mb-6" data-translate-key="aiAnalyzerDesc"></p>
                    <div class="max-w-2xl mx-auto">
                        <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm" rows="4" data-translate-key="aiAnalyzerPlaceholder" placeholder="" disabled></textarea>
                        <button class="mt-4 w-full bg-gray-400 text-white px-5 py-3 rounded-md font-bold cursor-not-allowed" data-translate-key="aiAnalyzerBtn"></button>
                    </div>
                </div>
            </div>
        </section>

        <section id="oem" class="py-20 bg-white">
            <div class="container mx-auto px-6">
                <h2 class="section-title" data-translate-key="oemTitle"></h2>
                <p class="section-subtitle" data-translate-key="oemSubtitle"></p>
                <div class="flex flex-col lg:flex-row-reverse gap-12 items-center">
                    <div class="lg:w-1/2">
                        <h3 class="text-2xl font-bold mb-4 text-gray-800" data-translate-key="oemCapabilitiesTitle"></h3>
                        <p class="text-gray-600 mb-6 leading-relaxed" data-translate-key="oemCapabilitiesDesc"></p>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-bold text-gray-700" data-translate-key="oemItem1Title"></h4>
                                <p class="text-sm text-gray-500" data-translate-key="oemItem1Desc"></p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-bold text-gray-700" data-translate-key="oemItem2Title"></h4>
                                <p class="text-sm text-gray-500" data-translate-key="oemItem2Desc"></p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-bold text-gray-700" data-translate-key="oemItem3Title"></h4>
                                <p class="text-sm text-gray-500" data-translate-key="oemItem3Desc"></p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-bold text-gray-700" data-translate-key="oemItem4Title"></h4>
                                <p class="text-sm text-gray-500" data-translate-key="oemItem4Desc"></p>
                            </div>
                        </div>
                    </div>
                    <div class="lg:w-1/2 w-full">
                        <div class="chart-container">
                            <canvas id="oemChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="contact" class="py-20 bg-gray-800 text-white">
            <div class="container mx-auto px-6 text-center">
                <h2 class="text-3xl font-bold mb-4" data-translate-key="contactTitle"></h2>
                <p class="mb-8 max-w-2xl mx-auto" data-translate-key="contactDesc"></p>
                <a href="mailto:<EMAIL>" class="bg-white text-gray-800 font-bold px-8 py-3 rounded-full hover:bg-gray-200 transition-colors" data-translate-key="contactBtn"></a>
            </div>
        </section>
    </main>

    <footer class="bg-[#333333] text-gray-400 py-8">
        <div class="container mx-auto px-6 text-center text-sm">
            <p data-translate-key="footerRights"></p>
            <p class="mt-2" data-translate-key="footerSlogan"></p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            
            const translations = {
                zh: {
                    brandName: "万和隆集团",
                    navAbout: "关于我们",
                    navCommercial: "工装服务",
                    navResidential: "家装服务",
                    navOem: "代工合作",
                    navContact: "联系我们",
                    heroTitle: "全案服务，构筑空间之美",
                    heroSubtitle: "从灵感到现实，万和隆是您值得信赖的伙伴",
                    aboutTitle: "关于我们",
                    aboutSubtitle: "ABOUT US",
                    aboutDesc: "万和隆集团，是一家以设计为驱动、以精工智造为基石的高端全案家具及工程服务提供商。我们不止步于提供卓越的单一产品，而是致力于为全球客户提供从空间规划、设计深化、工程施工到家具、灯饰、软装定制及落地的一体化解决方案。我们秉持着“匠心筑梦，和融共生”的企业哲学，无论是铸就品牌体验的星级酒店、承载企业雄心的商业地标，还是安放家庭温暖的私人居所，我们都以同样的敬畏之心与专业精神，倾力以赴。",
                    cardCommercialTitle: "工装服务",
                    cardCommercialDesc: "赋能商业愿景，塑造品牌地标。我们为酒店、企业、零售等商业空间提供一体化解决方案。",
                    cardResidentialTitle: "家装服务",
                    cardResidentialDesc: "定制理想生活，诠释家的温度。我们为高端私宅提供从心开始的全案定制服务。",
                    cardOemTitle: "代工合作",
                    cardOemDesc: "精工智造，成就卓越伙伴。我们为行业伙伴提供稳定、高效的生产制造服务。",
                    commercialTitle: "工装服务",
                    commercialSubtitle: "COMMERCIAL SERVICES",
                    commercialCoreTitle: "我们的核心服务领域",
                    commercialCoreDesc: "我们深刻理解商业空间是品牌战略、运营效率和用户体验的物理载体。我们的使命，就是将您的商业愿景，转化为具有强大感召力和商业价值的实体空间。",
                    commercialItem1Title: "酒店与度假村",
                    commercialItem1Desc: "我们最为核心和优势的业务板块，提供全方位FF&E及OS&E服务，助力提升酒店品牌价值。",
                    commercialItem2Title: "企业总部与办公空间",
                    commercialItem2Desc: "打造激发创造力、提升协作效率、彰显企业文化的现代办公环境。",
                    commercialItem3Title: "品牌展厅与零售空间",
                    commercialItem3Desc: "构建沉浸式消费场景，强化品牌形象，驱动销售增长。",
                    commercialChartTitle: "工装项目领域分布",
                    commercialChartLabel: "工装项目领域分布 (%)",
                    commercialChartCategories: ['酒店与度假村', '企业总部', '零售空间', '精装地产', '高端会所'],
                    aiGeneratorTitle: "✨ AI 空间概念生成器",
                    aiGeneratorDesc: "输入空间类型和您偏爱的风格，即刻获取一份由 AI 生成的专业设计概念。",
                    aiLabelSpaceType: "空间类型",
                    aiPlaceholderSpaceType: "例如：精品酒店大堂",
                    aiLabelDesignStyle: "设计风格",
                    aiPlaceholderDesignStyle: "例如：现代极简",
                    aiGenerateBtn: "生成概念",
                    aiResultPlaceholder: "您的设计概念将在此处显示...",
                    residentialTitle: "家装服务",
                    residentialSubtitle: "RESIDENTIAL SERVICES",
                    residentialDesc: "家，是心灵的归宿，是生活方式的终极体现。我们致力于为追求高品质生活的精英家庭，提供从“心”开始的“全案定制”服务。这是一场无忧的创造之旅。",
                    resiStep1Title: "深度沟通，聆听生活故事",
                    resiStep1Desc: "我们的服务始于一场朋友般的对话。我们将耐心了解您的家庭结构、生活习惯、兴趣爱好乃至未来规划，捕捉您对“理想之家”的真实构想。",
                    resiStep2Title: "量身设计，方案独一无二",
                    resiStep2Desc: "拒绝任何模板化的堆砌。我们的设计师将为您量身打造独一无二的空间方案，从空间动线、色彩体系，到材质搭配，每一处都呼应您的独特品味。",
                    resiStep3Title: "匠心工程，筑造安心港湾",
                    resiStep3Desc: "我们负责从土建改造、水电布局到环境系统集成的所有基础工程。以严苛的德系工艺标准，为您打造一个安全、健康、舒适、静谧的家之“硬核”。",
                    resiStep4Title: "一站落地，还原设计精髓",
                    resiStep4Desc: "您无需再为选购家具、灯具、窗帘、地毯而奔波。万和隆强大的产品库与定制能力，将确保所有软硬装元素完美统一，为您呈现一个“所见即所得”的梦想之家。",
                    aiAnalyzerTitle: "✨ AI 生活方式分析器 (即将推出)",
                    aiAnalyzerDesc: "描述您的家庭与日常，让 AI 为您解析最契合的生活空间方案。",
                    aiAnalyzerPlaceholder: "例如：我们是三口之家，喜欢周末在家看电影和阅读，希望有一个开放式的家庭活动区...",
                    aiAnalyzerBtn: "分析我的生活方式",
                    oemTitle: "代工合作",
                    oemSubtitle: "B2B PARTNERSHIP",
                    oemCapabilitiesTitle: "我们的制造能力",
                    oemCapabilitiesDesc: "我们是您品牌背后的坚实力量，是您卓越设计理念的忠实实现者。我们为全球范围内的家具品牌商、设计师事务所、大型地产及酒店采购商提供稳定、高效、高品质的生产制造服务。",
                    oemItem1Title: "先进的生产基地",
                    oemItem1Desc: "引进国际领先的自动化生产线。",
                    oemItem2Title: "严苛的品控体系",
                    oemItem2Desc: "覆盖全流程的质量管理。",
                    oemItem3Title: "强大的研发支持",
                    oemItem3Desc: "将创意概念转化为成熟产品。",
                    oemItem4Title: "柔性生产与交付",
                    oemItem4Desc: "满足小批量定制与规模化生产。",
                    oemChartTitle: "代工业务模式",
                    oemChartLabels: ['OEM (来样/来图加工)', 'ODM (设计开发制造)'],
                    contactTitle: "开启您的空间构想之旅",
                    contactDesc: "无论您有任何需求，我们的专家团队都乐于倾听并提供专业建议。即刻联系我们，让我们共同将您的愿景变为现实。",
                    contactBtn: "发送邮件",
                    footerRights: "© 2025 万和隆集团. All Rights Reserved.",
                    footerSlogan: "匠心筑梦，和融共生",
                    aiErrorInput: "请输入空间类型和设计风格。",
                    aiErrorFetch: "抱歉，生成时出现错误。请稍后再试。",
                    aiErrorParse: "未能获取有效的AI生成内容。"
                },
                en: {
                    brandName: "Wanhelong Group",
                    navAbout: "About Us",
                    navCommercial: "Commercial",
                    navResidential: "Residential",
                    navOem: "OEM/ODM",
                    navContact: "Contact Us",
                    heroTitle: "Holistic Service, Building the Beauty of Space",
                    heroSubtitle: "From inspiration to reality, Wanhelong is your trusted partner",
                    aboutTitle: "About Us",
                    aboutSubtitle: "ABOUT US",
                    aboutDesc: "Wanhelong Group is a high-end, full-service furniture and engineering provider driven by design and founded on precision manufacturing. We go beyond single products, offering integrated solutions from space planning, design, and construction to furniture, lighting, and soft furnishing customization and implementation for global clients. Upholding a philosophy of 'Crafting Dreams with Ingenuity, Thriving in Harmony,' we approach every project—be it a star-rated hotel, a corporate landmark, or a warm private residence—with the utmost respect and professionalism.",
                    cardCommercialTitle: "Commercial Services",
                    cardCommercialDesc: "Empowering business visions, shaping brand landmarks. We provide integrated solutions for commercial spaces like hotels, corporations, and retail.",
                    cardResidentialTitle: "Residential Services",
                    cardResidentialDesc: "Customizing ideal lifestyles, interpreting the warmth of home. We offer full-case custom services for high-end private residences.",
                    cardOemTitle: "OEM/ODM Partnership",
                    cardOemDesc: "Precision manufacturing, empowering exceptional partners. We provide stable and efficient manufacturing services for industry partners.",
                    commercialTitle: "Commercial Services",
                    commercialSubtitle: "COMMERCIAL SERVICES",
                    commercialCoreTitle: "Our Core Service Areas",
                    commercialCoreDesc: "We deeply understand that a commercial space is a physical embodiment of brand strategy, operational efficiency, and user experience. Our mission is to transform your business vision into a tangible space with strong appeal and commercial value.",
                    commercialItem1Title: "Hotels & Resorts",
                    commercialItem1Desc: "Our core and most advantageous business sector, providing comprehensive FF&E and OS&E services to enhance hotel brand value.",
                    commercialItem2Title: "Corporate Headquarters & Offices",
                    commercialItem2Desc: "Creating modern office environments that stimulate creativity, enhance collaboration, and reflect corporate culture.",
                    commercialItem3Title: "Brand Showrooms & Retail Spaces",
                    commercialItem3Desc: "Building immersive consumer scenes that strengthen brand image and drive sales growth.",
                    commercialChartTitle: "Commercial Project Distribution",
                    commercialChartLabel: "Commercial Project Distribution (%)",
                    commercialChartCategories: ['Hotels & Resorts', 'Headquarters', 'Retail Spaces', 'Real Estate', 'High-end Clubs'],
                    aiGeneratorTitle: "✨ AI Space Concept Generator",
                    aiGeneratorDesc: "Enter a space type and your preferred style to instantly get a professional design concept generated by AI.",
                    aiLabelSpaceType: "Space Type",
                    aiPlaceholderSpaceType: "e.g., Boutique Hotel Lobby",
                    aiLabelDesignStyle: "Design Style",
                    aiPlaceholderDesignStyle: "e.g., Modern Minimalist",
                    aiGenerateBtn: "Generate Concept",
                    aiResultPlaceholder: "Your design concept will be displayed here...",
                    residentialTitle: "Residential Services",
                    residentialSubtitle: "RESIDENTIAL SERVICES",
                    residentialDesc: "A home is a sanctuary for the soul, the ultimate expression of a lifestyle. We are dedicated to providing elite families with a 'full-case custom' service that starts from the heart. This is a worry-free creative journey.",
                    resiStep1Title: "In-depth Communication, Listening to Life Stories",
                    resiStep1Desc: "Our service begins with a friendly conversation. We listen patiently to understand your family structure, habits, hobbies, and future plans to capture your true vision of an 'ideal home'.",
                    resiStep2Title: "Tailored Design, Unique Solutions",
                    resiStep2Desc: "We reject all template-based approaches. Our designers create a unique spatial plan for you, where every detail, from flow to color schemes and materials, reflects your distinct taste.",
                    resiStep3Title: "Craftsmanship Engineering, Building a Safe Haven",
                    resiStep3Desc: "We handle all foundational work, from structural modifications to plumbing and electrical systems. Using rigorous German engineering standards, we build a safe, healthy, and comfortable 'core' for your home.",
                    resiStep4Title: "One-Stop Implementation, Restoring Design Essence",
                    resiStep4Desc: "No more hassle of shopping for furniture, lighting, or curtains. Wanhelong's vast product library and customization capabilities ensure all elements are perfectly unified, delivering a 'what you see is what you get' dream home.",
                    aiAnalyzerTitle: "✨ AI Lifestyle Analyzer (Coming Soon)",
                    aiAnalyzerDesc: "Describe your family and daily life, and let AI analyze the most suitable living space solution for you.",
                    aiAnalyzerPlaceholder: "e.g., We are a family of three who enjoy watching movies and reading at home on weekends. We hope for an open-plan family activity area...",
                    aiAnalyzerBtn: "Analyze My Lifestyle",
                    oemTitle: "OEM/ODM Partnership",
                    oemSubtitle: "B2B PARTNERSHIP",
                    oemCapabilitiesTitle: "Our Manufacturing Capabilities",
                    oemCapabilitiesDesc: "We are the solid force behind your brand, the faithful realizer of your excellent design concepts. We provide stable, efficient, and high-quality manufacturing services for furniture brands, design firms, and large-scale real estate and hotel purchasers worldwide.",
                    oemItem1Title: "Advanced Production Base",
                    oemItem1Desc: "Introducing leading international automated production lines.",
                    oemItem2Title: "Strict Quality Control System",
                    oemItem2Desc: "Comprehensive quality management covering the entire process.",
                    oemItem3Title: "Strong R&D Support",
                    oemItem3Desc: "Transforming creative concepts into mature products.",
                    oemItem4Title: "Flexible Production & Delivery",
                    oemItem4Desc: "Meeting needs for small-batch custom orders and large-scale production.",
                    oemChartTitle: "OEM/ODM Business Models",
                    oemChartLabels: ['OEM (Original Equipment Manufacturer)', 'ODM (Original Design Manufacturer)'],
                    contactTitle: "Start Your Space Creation Journey",
                    contactDesc: "Whatever your needs, our team of experts is happy to listen and provide professional advice. Contact us now to turn your vision into reality.",
                    contactBtn: "Send Email",
                    footerRights: "© 2025 Wanhelong Group. All Rights Reserved.",
                    footerSlogan: "Crafting Dreams with Ingenuity, Thriving in Harmony",
                    aiErrorInput: "Please enter both space type and design style.",
                    aiErrorFetch: "Sorry, an error occurred during generation. Please try again later.",
                    aiErrorParse: "Failed to get valid AI-generated content."
                }
            };

            let currentLanguage = 'zh';
            let commercialChart, oemChart;

            function updateContent(lang) {
                currentLanguage = lang;
                document.documentElement.lang = lang;

                document.querySelectorAll('[data-translate-key]').forEach(el => {
                    const key = el.dataset.translateKey;
                    const translation = translations[lang][key];
                    if (translation !== undefined) {
                        if (el.tagName === 'INPUT' || el.tagName === 'TEXTAREA') {
                            el.placeholder = translation;
                        } else {
                            el.innerHTML = translation;
                        }
                    }
                });

                document.getElementById('lang-zh').classList.toggle('active', lang === 'zh');
                document.getElementById('lang-en').classList.toggle('active', lang === 'en');
                
                // Re-initialize charts with translated text
                createCommercialChart(lang);
                createOemChart(lang);
            }

            function createCommercialChart(lang) {
                if (commercialChart) {
                    commercialChart.destroy();
                }
                const commercialCtx = document.getElementById('commercialChart').getContext('2d');
                commercialChart = new Chart(commercialCtx, {
                    type: 'bar',
                    data: {
                        labels: translations[lang].commercialChartCategories,
                        datasets: [{
                            label: translations[lang].commercialChartLabel,
                            data: [50, 20, 15, 10, 5],
                            backgroundColor: ['rgba(192, 160, 128, 0.8)','rgba(192, 160, 128, 0.6)','rgba(192, 160, 128, 0.5)','rgba(192, 160, 128, 0.4)','rgba(192, 160, 128, 0.3)'],
                            borderColor: 'rgba(192, 160, 128, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false },
                            title: { display: true, text: translations[lang].commercialChartTitle, color: '#333', font: { size: 16 } }
                        },
                        scales: {
                            x: { beginAtZero: true, grid: { color: '#eee' }, ticks: { color: '#666' } },
                            y: { grid: { display: false }, ticks: { color: '#666' } }
                        }
                    }
                });
            }

            function createOemChart(lang) {
                if (oemChart) {
                    oemChart.destroy();
                }
                const oemCtx = document.getElementById('oemChart').getContext('2d');
                oemChart = new Chart(oemCtx, {
                    type: 'doughnut',
                    data: {
                        labels: translations[lang].oemChartLabels,
                        datasets: [{
                            label: 'Business Model',
                            data: [65, 35],
                            backgroundColor: [ 'rgba(192, 160, 128, 0.8)', 'rgba(100, 100, 100, 0.5)' ],
                            hoverOffset: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { position: 'bottom' },
                            title: { display: true, text: translations[lang].oemChartTitle, color: '#333', font: { size: 16 } }
                        }
                    }
                });
            }

            document.getElementById('lang-zh').addEventListener('click', () => updateContent('zh'));
            document.getElementById('lang-en').addEventListener('click', () => updateContent('en'));

            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('main section');

            function changeNav() {
                let index = sections.length;
                while(--index && window.scrollY + 100 < sections[index].offsetTop) {}
                
                navLinks.forEach((link) => link.classList.remove('active'));
                const activeLink = document.querySelector(`.nav-link[href="#${sections[index].id}"]`);
                if(activeLink) {
                    activeLink.classList.add('active');
                }
            }

            changeNav();
            window.addEventListener('scroll', changeNav);

            const generateBtn = document.getElementById('generateConceptBtn');
            const resultDiv = document.getElementById('geminiResult');
            const spaceTypeInput = document.getElementById('spaceType');
            const designStyleInput = document.getElementById('designStyle');

            const simpleMarkdownToHtml = (text) => {
                return text
                    .replace(/### (.*)/g, '<h3 class="text-xl font-bold text-[#C0A080] mt-4 mb-2">$1</h3>')
                    .replace(/\n/g, '<br>');
            };
            
            async function generateConcept() {
                const spaceType = spaceTypeInput.value;
                const designStyle = designStyleInput.value;

                if (!spaceType || !designStyle) {
                    resultDiv.innerHTML = `<p class="text-red-500 text-center">${translations[currentLanguage].aiErrorInput}</p>`;
                    return;
                }

                resultDiv.innerHTML = '<div class="loader"></div>';
                generateBtn.disabled = true;
                generateBtn.classList.add('bg-gray-400', 'cursor-not-allowed');

                const promptLang = currentLanguage === 'zh' ? 'Chinese' : 'English';
                const prompt = `As an expert interior designer, create a design concept brief for a "${spaceType}" with a "${designStyle}" aesthetic. The response must be in ${promptLang} and structured with the following markdown headings: '### 设计理念' (or '### Design Concept' in English), '### 关键家具' (or '### Key Furniture'), '### 核心材质' (or '### Core Materials'), '### 色彩搭配' (or '### Color Palette'). Provide detailed and inspiring descriptions for each section.`;
                
                let chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
                const payload = { contents: chatHistory };
                const apiKey = "";
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`;

                try {
                    let response;
                    let retries = 3;
                    let delay = 1000;
                    for (let i = 0; i < retries; i++) {
                        response = await fetch(apiUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(payload)
                        });
                        if (response.ok) break;
                        await new Promise(res => setTimeout(res, delay));
                        delay *= 2;
                    }

                    if (!response.ok) {
                        throw new Error(`API request failed with status ${response.status}`);
                    }

                    const result = await response.json();
                    
                    if (result.candidates && result.candidates.length > 0 &&
                        result.candidates[0].content && result.candidates[0].content.parts &&
                        result.candidates[0].content.parts.length > 0) {
                        const text = result.candidates[0].content.parts[0].text;
                        resultDiv.innerHTML = `<div class="gemini-output text-gray-700 leading-relaxed">${simpleMarkdownToHtml(text)}</div>`;
                    } else {
                        throw new Error(translations[currentLanguage].aiErrorParse);
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<p class="text-red-500 text-center">${translations[currentLanguage].aiErrorFetch} ${error.message}</p>`;
                } finally {
                    generateBtn.disabled = false;
                    generateBtn.classList.remove('bg-gray-400', 'cursor-not-allowed');
                }
            }

            generateBtn.addEventListener('click', generateConcept);
            
            // Initial load
            updateContent(currentLanguage);
        });
    </script>
</body>
</html>
