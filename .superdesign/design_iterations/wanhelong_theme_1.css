
:root {
  --background: oklch(0.08 0 0);
  --foreground: oklch(0.95 0 0);
  --card: oklch(0.12 0 0);
  --card-foreground: oklch(0.95 0 0);
  --popover: oklch(0.12 0 0);
  --popover-foreground: oklch(0.95 0 0);
  --primary: oklch(0.75 0.15 85);
  --primary-foreground: oklch(0.08 0 0);
  --secondary: oklch(0.2 0 0);
  --secondary-foreground: oklch(0.9 0 0);
  --muted: oklch(0.15 0 0);
  --muted-foreground: oklch(0.6 0 0);
  --accent: oklch(0.75 0.15 85);
  --accent-foreground: oklch(0.08 0 0);
  --destructive: oklch(0.5 0.25 25);
  --destructive-foreground: oklch(0.95 0 0);
  --border: oklch(0.2 0 0);
  --input: oklch(0.2 0 0);
  --ring: oklch(0.75 0.15 85);
  --chart-1: oklch(0.75 0.15 85);
  --chart-2: oklch(0.65 0.12 85);
  --chart-3: oklch(0.55 0.1 85);
  --chart-4: oklch(0.45 0.08 85);
  --chart-5: oklch(0.35 0.06 85);
  --sidebar: oklch(0.08 0 0);
  --sidebar-foreground: oklch(0.95 0 0);
  --sidebar-primary: oklch(0.75 0.15 85);
  --sidebar-primary-foreground: oklch(0.08 0 0);
  --sidebar-accent: oklch(0.2 0 0);
  --sidebar-accent-foreground: oklch(0.95 0 0);
  --sidebar-border: oklch(0.2 0 0);
  --sidebar-ring: oklch(0.75 0.15 85);
  --font-sans: "Geist Mono", monospace;
  --font-serif: "Source Serif Pro", serif;
  --font-mono: "Geist Mono", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 2px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.1);
  --shadow-sm: 0 2px 8px 0px hsl(0 0% 0% / 0.1);
  --shadow: 0 4px 12px 0px hsl(0 0% 0% / 0.1);
  --shadow-md: 0 6px 16px 0px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0 8px 24px 0px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0 12px 32px 0px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 20px 48px 0px hsl(0 0% 0% / 0.1);
  --tracking-normal: 0em;
  --spacing: 0.5rem;
}
